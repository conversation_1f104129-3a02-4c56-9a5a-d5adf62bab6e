"use node";

import { generateText } from "ai";
import { api } from "../../_generated/api";
import { createAvailableTools } from "../tools";
import {
  createAIModel,
  getProviderApiKey,
  getDefaultModel,
  getModelInfo,
  SupportedProvider,
} from "../providers";
import { PROVIDER_BASE_URLS } from "../providers/constants";
import { getUserFriendlyErrorMessage } from "../utils/errors";

export async function generateNonStreamingResponse(
  ctx: any,
  args: {
    conversationId: string;
    branchId?: string;
    messages: Array<{
      role: string;
      content:
        | string
        | Array<{
            type: "text" | "image" | "file";
            text?: string;
            image?: string;
            file?: string;
            data?: string;
            mimeType?: string;
          }>;
    }>;
    provider?: SupportedProvider;
    model?: string;
    temperature?: number;
    enabledTools?: string[];
    thinkingBudget?: string | number;
    persona?: string;
    recipe?: string;
  }
) {
  const provider = (args.provider as SupportedProvider) || "openai";
  const model = args.model ?? getDefaultModel(provider);
  const temperature = args.temperature ?? 1;
  const enabledTools = args.enabledTools ?? [];
  const branchId = args.branchId;
  const personaId = args.persona || "none";
  const recipeId = args.recipe || "none";

  let usingUserKey = false;
  let abortController: AbortController | null = null;

  try {
    // Create an AbortController for timeout handling
    abortController = new AbortController();

    // Set timeout based on provider - Gemini models need longer timeouts
    const timeoutMs = provider === "google" ? 600000 : 300000; // 10 min for Google, 5 min for others
    const timeoutId = setTimeout(() => {
      if (abortController && !abortController.signal.aborted) {
        abortController.abort(
          new Error(`Request timeout after ${timeoutMs / 1000} seconds`)
        );
      }
    }, timeoutMs);

    // Process multimodal content if needed
    const processedMessages = await Promise.all(
      args.messages.map(async (message) => {
        if (Array.isArray(message.content)) {
          const processedContent = await Promise.all(
            message.content.map(async (part) => {
              if (part.type === "image") {
                const imageValue = part.image;

                // Check if this is a storage ID (Convex storage IDs are alphanumeric)
                if (imageValue?.match(/^[a-z0-9]{28,}$/)) {
                  try {
                    const publicUrl = await ctx.storage.getUrl(
                      imageValue as any
                    );
                    if (publicUrl) {
                      return {
                        type: "image",
                        image: publicUrl,
                      };
                    } else {
                      console.warn(
                        `Failed to get public URL for storage ID: ${imageValue}`
                      );
                      return part;
                    }
                  } catch (error) {
                    console.error(
                      `Error getting public URL for storage ID ${imageValue}:`,
                      error
                    );
                    return part;
                  }
                }

                return part;
              } else if (part.type === "file") {
                const fileValue = (part as any).file ?? (part as any).data;

                // Check if this is a storage ID
                if (fileValue?.match?.(/^[a-z0-9]{28,}$/)) {
                  try {
                    // Get file metadata using the system table (recommended approach)
                    const fileMetadata = await ctx.db.system.get(fileValue);
                    const publicUrl = await ctx.storage.getUrl(fileValue);

                    if (publicUrl && fileMetadata) {
                      return {
                        type: "file",
                        data: publicUrl,
                        mimeType:
                          fileMetadata.contentType ??
                          "application/octet-stream",
                      };
                    } else {
                      console.warn(
                        `Failed to get public URL or metadata for file storage ID: ${fileValue}`
                      );
                      return part;
                    }
                  } catch (error) {
                    console.error(
                      `Error getting public URL for file storage ID ${fileValue}:`,
                      error
                    );
                    return part;
                  }
                }

                return part;
              }
              return part;
            })
          );
          return { ...message, content: processedContent };
        }
        return message;
      })
    );

    // Get API Key for the selected provider
    const apiKeyRecord = await ctx.runQuery(api.apiKeys.getByProvider, {
      provider,
    });

    const { apiKey, usingUserKey: userKeyFlag } = getProviderApiKey(
      provider,
      apiKeyRecord
    );
    usingUserKey = userKeyFlag;

    if (!apiKey) {
      throw new Error(
        `No API key available for ${provider}. Please configure your API key in settings or use a provider with built-in support.`
      );
    }

    // Only check usage limits if using built-in keys
    if (!usingUserKey && provider !== "openrouter") {
      // Estimate token usage for credit check (rough estimate)
      const estimatedInputTokens = processedMessages.reduce((total, msg) => {
        if (typeof msg.content === "string") {
          return total + Math.ceil(msg.content.length / 4); // Rough estimate: 4 chars per token
        }
        // Handle array content
        const textContent = msg.content
          .filter((part) => part.type === "text")
          .map((part) => {
            // Safe to cast since we've filtered for text type
            const textPart = part as { type: "text"; text?: string };
            return textPart.text || "";
          })
          .join(" ");
        return total + Math.ceil(textContent.length / 4);
      }, 0);
      const estimatedOutputTokens = 2000; // Conservative estimate

      const creditCheck = await ctx.runQuery(api.usage.checkCreditsAvailable, {
        model,
        estimatedInputTokens,
        estimatedOutputTokens,
      });

      if (!creditCheck.hasCredits) {
        throw new Error(
          `Insufficient credits. Required: ${creditCheck.requiredCredits}, Available: ${creditCheck.availableCredits}. Add your own API keys in settings for unlimited usage.`
        );
      }

      if (creditCheck.wouldExceedSpending) {
        throw new Error(
          `This request would exceed your monthly spending limit. Add your own API keys in settings for unlimited usage.`
        );
      }
    }

    // Get MCP servers for the user
    const { getAuthUserId } = await import("@convex-dev/auth/server");
    const userId = await getAuthUserId(ctx);
    let mcpServers: any[] = [];
    let n8nServers: any[] = [];
    let n8nWorkflows: any[] = [];

    if (userId) {
      mcpServers = await ctx.runQuery(api.mcpServers.listEnabled, {});
      // Load server-based n8n configuration
      n8nServers = await ctx.runQuery(api.n8nServers.listEnabled, {});
      // Legacy workflow-based configuration (optional)
      n8nWorkflows = await ctx
        .runQuery(api.n8nWorkflows.listEnabled, {})
        .catch(() => []);
    }

    // Create tools based on enabled tools and model capabilities
    const availableTools = await createAvailableTools(
      ctx,
      enabledTools,
      model,
      usingUserKey,
      mcpServers,
      n8nServers,
      n8nWorkflows
    );

    // Check if model supports tools
    const modelInfo = getModelInfo(model);
    const hasTools = Object.keys(availableTools).length > 0;

    // Get user preferences and instructions to build system prompt
    const userPreferences = await ctx.runQuery(api.preferences.get);
    const userInstructions = await ctx.runQuery(api.userInstructions.get);

    // Prepare messages with system prompt if enabled
    let messagesWithSystemPrompt = [...processedMessages];

    // Check if there's already a system message
    const hasSystemMessage = processedMessages.some(
      (msg) => msg.role === "system"
    );

    if (!hasSystemMessage) {
      let systemContent = "";

      // Include today's date in the system prompt (YYYY-MM-DD)
      const todayDate = new Date().toISOString().split("T")[0];

      if (
        userPreferences?.useCustomSystemPrompt &&
        userPreferences?.systemPrompt
      ) {
        // Use custom system prompt
        systemContent =
          `Current date: ${todayDate}\n\n` + userPreferences.systemPrompt;
      } else {
        // Use default system prompt when custom is disabled
        systemContent =
          `Current date: ${todayDate}\n\n` +
          "You are ErzenAI, an autonomous AI agent that can reason, plan, and act to accomplish the user's goals. You have access to specialised external tools (e.g. webSearch, calculator, codeAnalysis, imageGeneration, memory, and others) and can decide when and how to combine them.\n\n## Agent Behaviour\n1. Think step-by-step and sketch a short internal plan before answering (use the native thinking channel).\n2. If a tool is helpful, choose the best one, provide precise arguments, and wait for its result.\n3. Reflect on the result, decide whether additional tool calls are needed (you may chain multiple calls), and continue until you have the information required.\n4. Synthesise all insights into a clear, actionable response for the user.\n\n## Tool Guidelines\n- Use tools only when they add value beyond your own knowledge.\n- Chain tool calls when beneficial (e.g. search → calculator → codeAnalysis).\n- Avoid redundant or unnecessary calls; be efficient.\n- Validate and cross-check tool results for consistency.\n\n## Communication Style\n- Friendly, professional, concise yet complete.\n- Ask clarifying questions if the request is ambiguous.\n- Use numbered or bulleted lists for multi-step explanations.\n- Provide a brief summary or next steps when appropriate.\n\nRemember: You are a powerful agent that can seamlessly combine internal reasoning with external tool usage to deliver the best possible outcome.";
      }

      // Append persona prompt if provided
      const PERSONA_PROMPTS: Record<string, string> = {
        companion:
          "You are the user's compassionate companion. Respond with warmth, empathy and encouragement. Prioritise emotional support over facts.",
        friend:
          "You are the user's close friend. Keep the tone casual, supportive and lightly humorous. Use informal language, contractions and emojis when appropriate.",
        comedian:
          "You are a stand-up comedian. Deliver responses with wit and humour while still addressing the user's topic. Make sure jokes are light-hearted and never offensive.",
        not_a_doctor:
          "You are NOT a medical professional. If the user requests medical advice you must disclaim you are not a doctor and encourage consulting a qualified physician. Provide general information only.",
        not_a_therapist:
          "You are NOT a mental-health professional. Provide supportive, non-clinical responses and encourage the user to seek professional help for serious issues.",
      };

      if (personaId !== "none" && PERSONA_PROMPTS[personaId]) {
        systemContent = PERSONA_PROMPTS[personaId] + "\n\n" + systemContent;
      }

      // Append recipe prompt if provided
      const RECIPE_PROMPTS: Record<string, string> = {
        summarise:
          "Whenever the user provides text, summarise it concisely using bullet-points. If the text is short, provide a one-sentence summary.",
        translate_es:
          "Translate all user input into Spanish. Respond ONLY with the translation, no explanations.",
        brainstorm:
          "Generate a creative list of at least 10 varied ideas that satisfy the user's request. Encourage originality and diversity.",
        email_draft:
          "Craft a professional, well-structured email based on the user's instructions. Use a polite tone and clear formatting.",
      };

      if (recipeId !== "none" && RECIPE_PROMPTS[recipeId]) {
        systemContent += "\n\n" + RECIPE_PROMPTS[recipeId];
      }

      // Add user instructions if available
      if (userInstructions && userInstructions.trim()) {
        systemContent +=
          "\n\nAdditional user instructions:\n" + userInstructions;
      }

      // Add system prompt at the beginning
      messagesWithSystemPrompt = [
        {
          role: "system" as const,
          content: systemContent,
        },
        ...processedMessages,
      ];
    }

    // Create AI model instance with native thinking support
    const { model: nonStreamingAiModel, providerOptions } = createAIModel({
      provider,
      model,
      apiKey,
      baseUrl: PROVIDER_BASE_URLS[provider],
      temperature,
    });

    // Generate the response using generateText - conditionally include tools
    const generateTextConfig: any = {
      model: nonStreamingAiModel,
      messages: messagesWithSystemPrompt as any,
      temperature,
      maxSteps: 25,
      abortSignal: abortController.signal,
      // Include native thinking support via provider options
      providerOptions,
    };

    // Only add tools if model supports them AND we have tools to add
    if (modelInfo.supportsTools && hasTools) {
      generateTextConfig.tools = availableTools;
    }

    const result = await generateText(generateTextConfig);

    clearTimeout(timeoutId);

    // Only update usage if using built-in keys
    if (!usingUserKey) {
      // Estimate token usage for credit deduction (since we don't have exact metrics in non-streaming)
      const inputTokens = processedMessages.reduce((total, msg) => {
        if (typeof msg.content === "string") {
          return total + Math.ceil(msg.content.length / 4); // Rough estimate: 4 chars per token
        }
        // Handle array content
        const textContent = msg.content
          .filter((part) => part.type === "text")
          .map((part) => {
            // Safe to cast since we've filtered for text type
            const textPart = part as { type: "text"; text?: string };
            return textPart.text || "";
          })
          .join(" ");
        return total + Math.ceil(textContent.length / 4);
      }, 0);
      const outputTokens = Math.ceil((result.text || "").length / 4); // Estimate output tokens

      if (inputTokens > 0 || outputTokens > 0) {
        try {
          await ctx.runMutation(api.usage.deductCredits, {
            model,
            inputTokens,
            outputTokens,
          });
        } catch (creditError) {
          console.warn("Failed to deduct credits:", creditError);
          // Don't fail the whole request for credit deduction errors
        }
      }
    }

    // Save the assistant's response with proper tool calls format
    const toolCalls =
      result.toolCalls?.map((call, index) => {
        const toolResult = result.toolResults?.[index] as any;
        return {
          id: call.toolCallId || `tool_${Date.now()}_${index}`,
          name: call.toolName,
          arguments: JSON.stringify(call.args),
          result: toolResult?.result
            ? JSON.stringify(toolResult.result)
            : undefined,
        };
      }) || undefined;

    // Ensure thinking doesn't contain duplicate content
    let finalContent =
      result.text?.trim() ||
      (typeof result.reasoning === "string" ? result.reasoning.trim() : "");

    let finalThinking =
      typeof result.reasoning === "string" ? result.reasoning.trim() : "";

    if (!finalContent && finalThinking) {
      finalContent = finalThinking;
      finalThinking = "";
    }

    await ctx.runMutation(api.messages.add, {
      conversationId: args.conversationId,
      branchId: branchId,
      role: "assistant",
      content:
        finalContent ||
        "I apologize, but I couldn't generate a response. The model may have returned empty content or encountered an issue during generation.",
      thinking: finalThinking || undefined,
      toolCalls,
    });

    return {
      text: result.text,
      toolCalls: result.toolCalls,
      usingUserKey,
    };
  } catch (error) {
    // Clear timeout if it exists
    if (abortController) {
      abortController.abort();
    }

    let errorMessage: string;

    // Handle timeout errors specifically
    if (error instanceof Error && error.message.includes("timeout")) {
      errorMessage = `The ${provider} model (${model}) timed out. This can happen with complex requests or when the model is under heavy load. Please try again or consider using a different model.`;
    } else if (error instanceof Error && error.message.includes("aborted")) {
      errorMessage = `Request was cancelled: ${error.message}`;
    } else {
      errorMessage = getUserFriendlyErrorMessage(error, provider, usingUserKey);
    }

    console.error(`Generation error for ${provider}/${model}:`, error);

    // Save error message as assistant response
    await ctx.runMutation(api.messages.add, {
      conversationId: args.conversationId,
      branchId: branchId,
      role: "assistant",
      content: errorMessage,
      generationMetrics: { provider, model, generationTimeMs: 0 },
      isError: true,
    });

    return {
      error: errorMessage,
      usingUserKey,
    };
  }
}
