{"name": "erzen-ai", "private": false, "version": "1.0.0-beta", "type": "module", "scripts": {"dev": "npm-run-all --parallel dev:frontend dev:backend", "dev:frontend": "vite --open", "dev:backend": "convex dev", "build": "vite build", "lint": "tsc -p convex -noEmit --pretty false && tsc -p . -noEmit --pretty false && convex dev --once && vite build"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/cerebras": "^0.2.14", "@ai-sdk/cohere": "^1.2.10", "@ai-sdk/deepseek": "^0.2.14", "@ai-sdk/google": "^1.2.19", "@ai-sdk/groq": "^1.2.9", "@ai-sdk/mistral": "^1.2.8", "@ai-sdk/openai": "^1.3.22", "@ai-sdk/openai-compatible": "^0.2.15", "@auth/core": "^0.40.0", "@convex-dev/auth": "^0.0.87", "@dmitryrechkin/json-schema-to-zod": "^1.0.1", "@google/genai": "^1.7.0", "@modelcontextprotocol/sdk": "^1.13.2", "@openrouter/ai-sdk-provider": "^0.7.2", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/typography": "^0.5.16", "@tsparticles/engine": "^3.8.1", "@tsparticles/react": "^3.0.0", "@types/react-syntax-highlighter": "^15.5.13", "@types/recharts": "^2.0.1", "ai": "^4.3.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "convex": "^1.25.1", "crypto": "^1.0.1", "dedent": "^1.6.0", "ignore": "^7.0.5", "katex": "^0.16.22", "lucide-react": "^0.525.0", "mermaid": "^11.7.0", "next-themes": "^0.4.6", "node-fetch": "3", "octokit": "^5.0.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "react-tsparticles": "^2.12.2", "recharts": "^3.0.2", "rehype-autolink-headings": "^7.1.0", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "rehype-slug": "^6.0.0", "remark-breaks": "^4.0.0", "remark-directive": "^4.0.0", "remark-emoji": "^5.0.1", "remark-frontmatter": "^5.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "remark-parse": "^11.0.0", "remark-rehype": "^11.1.2", "remark-unwrap-images": "^5.0.0", "remark-wiki-link": "^2.0.1", "shadcn-ui": "^0.9.5", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "tsparticles": "^3.8.1", "zod": "^3.25.67"}, "devDependencies": {"@eslint/js": "^9.30.0", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^24.0.7", "@types/node-fetch": "2", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "@welldone-software/why-did-you-render": "^10.0.1", "autoprefixer": "~10", "dotenv": "^17.0.0", "eslint": "^9.30.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "npm-run-all": "^4.1.5", "postcss": "~8", "prettier": "^3.6.2", "tailwindcss": "~4", "typescript": "~5.8.3", "typescript-eslint": "^8.35.0", "vite": "^7.0.0"}, "pnpm": {"onlyBuiltDependencies": ["esbuild"]}}