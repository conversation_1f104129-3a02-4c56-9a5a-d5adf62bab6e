import { useState } from "react";
import { useQ<PERSON>y, useMutation } from "convex/react";
import { toast } from "sonner";
import { api } from "../../../convex/_generated/api";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Eye,
  EyeOff,
  Trash2,
  Zap,
  Infinity as InfinityIcon,
  Lock,
} from "lucide-react";
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@/components/ui/alert";
import { Doc } from "../../../convex/_generated/dataModel";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { PROVIDER_CONFIGS } from "@/lib/models";

type ApiKeyProvider = Doc<"apiKeys">["provider"];

const API_KEY_PROVIDER_CONFIGS = {
  openai: {
    name: "OpenAI",
    models: [
      "gpt-4o-mini-2024-07-18",
      "chatgpt-4o-latest", 
      "o3-mini",
      "o4-mini",
      "gpt-4.1",
      "gpt-4.1-mini",
      "gpt-4.1-nano",
      "gpt-4o",
      "gpt-4o-mini",
      "gpt-4-turbo",
      "gpt-3.5-turbo"
    ],
    keyPlaceholder: "sk-...",
    description: "GPT models from OpenAI",
    hasBuiltIn: true,
  },
  google: {
    name: "Google AI",
    models: [
      "gemini-2.0-flash",
      "gemini-2.0-flash-lite", 
      "gemini-2.5-pro-preview-05-06",
      "gemini-2.5-flash-preview-05-20",
      "gemini-1.5-pro",
      "gemini-1.5-flash"
    ],
    keyPlaceholder: "AIza...",
    description: "Gemini models from Google",
    hasBuiltIn: true,
  },
  anthropic: {
    name: "Anthropic",
    models: [
      "claude-sonnet-4-20250514",
      "claude-opus-4-20250514",
      "claude-3-7-sonnet-latest",
      "claude-3-5-sonnet-latest",
      "claude-3-5-haiku-latest",
      "claude-3-5-sonnet-20241022",
      "claude-3-haiku-20240307",
      "claude-3-sonnet-20240229",
      "claude-3-opus-20240229"
    ],
    keyPlaceholder: "sk-ant-...",
    description: "Claude models from Anthropic",
    hasBuiltIn: true,
  },
  openrouter: {
    name: "OpenRouter",
    models: [
      "deepseek/deepseek-chat-v3-0324:free",
      "deepseek/deepseek-r1:free",
      "tngtech/deepseek-r1t-chimera:free",
      "deepseek/deepseek-prover-v2:free",
      "mistralai/devstral-small:free",
      "qwen/qwen2.5-vl-72b-instruct:free",
      "mistralai/mistral-small-3.1-24b-instruct:free",
      "google/gemma-3-27b-it:free",
      "rekaai/reka-flash-3:free",
      "google/gemini-2.5-pro-exp-03-25:free",
      "qwen/qwen3-235b-a22b:free",
      "qwen/qwen3-30b-a3b:free",
      "qwen/qwen3-32b:free",
      "nvidia/llama-3.1-nemotron-ultra-253b-v1:free",
      "anthropic/claude-3.5-sonnet",
      "openai/gpt-4o",
      "google/gemini-pro-1.5",
      "meta-llama/llama-3.1-405b-instruct",
      "mistralai/mixtral-8x7b-instruct",
      "cohere/command-r-plus",
    ],
    keyPlaceholder: "sk-or-...",
    description: "Access to multiple AI models",
    hasBuiltIn: true,
  },
  groq: {
    name: "Groq",
    models: [
      "deepseek-r1-distill-llama-70b",
      "deepseek-r1-distill-qwen-32b",
      "llama-3.3-70b-versatile",
      "llama-3.2-90b-vision-preview",
      "llama3-70b-8192",
      "qwen-qwq-32b",
      "meta-llama/llama-4-scout-17b-16e-instruct",
      "meta-llama/llama-4-maverick-17b-128e-instruct",
      "compound-beta",
      "compound-beta-mini",
      "llama-3.1-405b-reasoning",
      "llama-3.1-70b-versatile",
      "llama-3.1-8b-instant",
      "mixtral-8x7b-32768",
      "gemma2-9b-it",
    ],
    keyPlaceholder: "gsk_...",
    description: "Ultra-fast inference",
    hasBuiltIn: true,
  },
  deepseek: {
    name: "DeepSeek",
    models: ["deepseek-chat", "deepseek-coder"],
    keyPlaceholder: "sk-...",
    description: "Reasoning and coding models",
    hasBuiltIn: true,
  },
  grok: {
    name: "Grok (xAI)",
    models: ["grok-beta", "grok-vision-beta"],
    keyPlaceholder: "xai-...",
    description: "Elon's AI with real-time data",
    hasBuiltIn: true,
  },
  cohere: {
    name: "Cohere",
    models: ["command-r-plus", "command-r", "command"],
    keyPlaceholder: "co_...",
    description: "Enterprise-grade language models",
    hasBuiltIn: true,
  },
  mistral: {
    name: "Mistral AI",
    models: [
      "accounts/fireworks/models/mistral-small-24b-instruct-2501",
      "mistral-large-latest",
      "mistral-medium-latest",
      "mistral-small-latest", 
      "codestral-latest",
    ],
    keyPlaceholder: "...",
    description: "European AI models",
    hasBuiltIn: true,
  },
  tavily: {
    name: "Tavily Search",
    models: [],
    keyPlaceholder: "tvly-...",
    description: "Real-time web search API",
    hasBuiltIn: true,
  },
  openweather: {
    name: "OpenWeatherMap",
    models: [],
    keyPlaceholder: "...",
    description: "Weather data API",
    hasBuiltIn: true,
  },
  cerebras: {
    name: "Cerebras",
    models: [
      "llama-4-scout-17b-16e-instruct",
      "llama3.1-8b",
      "llama-3.3-70b",
      "qwen-3-32b",
      "deepseek-r1-distill-llama-70b-cerebras",
    ],
    keyPlaceholder: "csk-...",
    description: "Ultra-fast inference with Cerebras",
    hasBuiltIn: true,
  },
  firecrawl: {
    name: "Firecrawl",
    models: [],
    keyPlaceholder: "fc-...",
    description: "AI-ready web scraping and crawling",
    hasBuiltIn: true,
  },
  github: {
    name: "GitHub",
    models: [
      "gpt-4o",
      "gpt-4o-mini",
      "o1-preview",
      "o1-mini",
      "Phi-3-medium-128k-instruct",
      "Phi-3-medium-4k-instruct",
      "Phi-3-mini-128k-instruct",
      "Phi-3-mini-4k-instruct",
      "Phi-3-small-128k-instruct",
      "Phi-3-small-8k-instruct",
      "Phi-3.5-mini-instruct",
      "Phi-3.5-MoE-instruct",
      "Phi-3.5-vision-instruct",
      "Meta-Llama-3-70B-Instruct",
      "Meta-Llama-3-8B-Instruct",
      "Meta-Llama-3.1-405B-Instruct",
      "Meta-Llama-3.1-70B-Instruct",
      "Meta-Llama-3.1-8B-Instruct",
      "Meta-Llama-3.2-11B-Vision-Instruct",
      "Meta-Llama-3.2-90B-Vision-Instruct",
      "Mistral-large",
      "Mistral-large-2407",
      "Mistral-Nemo",
      "Mistral-small",
      "cohere-command-r",
      "cohere-command-r-plus",
      "AI21-Jamba-1.5-Large",
      "AI21-Jamba-1.5-Mini",
    ],
    keyPlaceholder: "ghp_... or github_pat_...",
    description: "GitHub Models inference API with PAT token",
    hasBuiltIn: true,
  },
};

export function ApiKeysSection() {
  const apiKeyInfo = useQuery(api.apiKeys.getApiKeyInfo) || [];
  const upsertApiKey = useMutation(api.apiKeys.upsert);
  const removeApiKey = useMutation(api.apiKeys.remove);

  const [apiKeys, setApiKeys] = useState<Record<string, string>>({});
  const [showKeys, setShowKeys] = useState<Record<string, boolean>>({});

  const handleSave = async (provider: ApiKeyProvider, key: string) => {
    if (!key) {
      toast.error("API key cannot be empty.");
      return;
    }
    toast.promise(
      upsertApiKey({
        provider,
        apiKey: key,
      }),
      {
        loading: "Saving API key...",
        success: "API key saved!",
        error: "Failed to save API key.",
      },
    );
  };

  const handleRemove = async (provider: ApiKeyProvider) => {
    toast.promise(
      removeApiKey({ provider }),
      {
        loading: "Removing API key...",
        success: "API key removed!",
        error: "Failed to remove API key.",
      },
    );
  };

  return (
    <div className="space-y-6">
      <Alert className="shadow-sm rounded-lg">
        <Zap className="h-4 w-4" />
        <AlertTitle>Built-in vs. Your Own Keys</AlertTitle>
        <AlertDescription className="space-y-2">
          <p>
            <strong>Built-in Keys:</strong> Free usage with monthly limits
            (messages & searches count)
          </p>
          <p>
            <strong>Your Keys:</strong> {" "}
            <InfinityIcon className="inline w-5 h-5" /> Unlimited usage - no
            limits or counting!
          </p>
        </AlertDescription>
      </Alert>
      <Alert className="shadow-sm rounded-lg">
        <Lock className="h-4 w-4" />
        <AlertTitle>🔒 Encryption & Security</AlertTitle>
        <AlertDescription>
          <p>
            <strong>All API keys are encrypted</strong> before being stored in
            the database. Your keys are protected with industry-standard
            encryption.
          </p>
        </AlertDescription>
      </Alert>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {Object.entries(PROVIDER_CONFIGS).map(([provider, config]) => {
          const keyInfo = apiKeyInfo.find(
            (info) => info.provider === provider,
          );
          const hasUserKey = keyInfo?.hasUserKey || false;

          return (
            <Card key={provider} className="shadow-md border border-border/20 rounded-xl hover:shadow-lg transition-shadow duration-200">
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg font-semibold">{config.name}</CardTitle>
                  <Badge variant={hasUserKey ? "default" : "secondary"} className="text-xs">
                    {hasUserKey ? "Custom Key" : "Built-in"}
                  </Badge>
                </div>
                <CardDescription className="text-sm mt-1">{config.description}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4 p-6 pt-0">
                <Separator className="my-2" />
                <div className="flex items-center gap-2">
                  <div className="relative flex-1">
                    <Input
                      type={showKeys[provider] ? "text" : "password"}
                      placeholder={config.keyPlaceholder}
                      value={apiKeys[provider] ?? ""}
                      onChange={(e) =>
                        setApiKeys({ ...apiKeys, [provider]: e.target.value })
                      }
                      className="pr-10"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() =>
                        setShowKeys({ ...showKeys, [provider]: !showKeys[provider] })
                      }
                    >
                      {showKeys[provider] ? (
                        <EyeOff className="h-4 w-4 text-muted-foreground" />
                      ) : (
                        <Eye className="h-4 w-4 text-muted-foreground" />
                      )}
                    </Button>
                  </div>
                </div>
                <div className="flex flex-wrap gap-2">
                  <Button
                    onClick={() =>
                      void handleSave(provider as ApiKeyProvider, apiKeys[provider] ?? "")
                    }
                    disabled={!apiKeys[provider]?.trim()}
                    className="flex-1 sm:flex-none text-sm py-2 px-4"
                  >
                    Save Key
                  </Button>
                  {hasUserKey && (
                    <Button
                      variant="destructive"
                      onClick={() => void handleRemove(provider as ApiKeyProvider)}
                      className="flex-1 sm:flex-none text-sm py-2 px-4"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Remove
                    </Button>
                  )}
                </div>
                {hasUserKey && keyInfo?.keyPreview && (
                  <p className="text-sm text-muted-foreground mt-2">
                    Key: {keyInfo.keyPreview} • Added{" "}
                    {keyInfo.addedAt
                      ? new Date(keyInfo.addedAt).toLocaleDateString()
                      : "Recently"}
                  </p>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
} 